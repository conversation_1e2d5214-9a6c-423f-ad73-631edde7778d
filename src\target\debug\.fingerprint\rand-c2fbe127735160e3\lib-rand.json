{"rustc": 12677941269563601140, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 11067538300610930473, "deps": [[1573238666360410412, "rand_chacha", false, 1734991261392224062], [18130209639506977569, "rand_core", false, 18222235949486022340]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-c2fbe127735160e3\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}