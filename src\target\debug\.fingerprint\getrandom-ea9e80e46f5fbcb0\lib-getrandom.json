{"rustc": 12677941269563601140, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 2520442750682607784, "deps": [[10411997081178400487, "cfg_if", false, 7208692441487477134]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-ea9e80e46f5fbcb0\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}