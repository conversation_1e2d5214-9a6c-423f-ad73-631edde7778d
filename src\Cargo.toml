[package]
name = "rust_indirect_syscalls"
version = "0.1.0"
edition = "2021"



[[bin]]
name = "main"
path = "src/main.rs"

# Increase stack size to 64MB to prevent stack overflow
[profile.dev]
stack-size = 67108864  # 64MB in bytes

[profile.release]
stack-size = 67108864  # 64MB in bytes



[dependencies]
rand = "0.8.5"
lazy_static = "1.4.0"
once_cell = "1.21.3"
parking_lot = "0.12.4"

[build-dependencies]
cc = "1.0"

[dependencies.windows-sys]
version = "0.52"
features = [
    "Win32_System_LibraryLoader",       # For GetModuleHandleA, GetProcAddress
    "Win32_System_Memory",              # For MEM_COMMIT, PAGE_READWRITE, etc.
    "Win32_Foundation",                 # For NTSTATUS, HANDLE, FARPROC, STATUS_SUCCESS
    "Win32_System_Threading",           # For GetCurrentProcess, CreateFiber, SwitchToFiber
    "Win32_System_SystemServices",      # For IMAGE_DOS_HEADER, IMAGE_DOS_SIGNATURE
    "Win32_System_Diagnostics_Debug",   # For WriteProcessMemory
    "Win32_System_Performance",         # For QueryPerformanceCounter, QueryPerformanceFrequency
    "Win32_System_SystemInformation",   # For GetTickCount64
]
